# Story 2.3: 锯齿状腺瘤通路实现

## Status

Draft

## Story

**As a** 模拟引擎，
**I want** 建模锯齿状腺瘤-癌变进展通路，
**so that** 模拟结直肠癌发展的替代路径（15%的病例）。

## Acceptance Criteria

1. 实现锯齿状腺瘤疾病状态（正常→小锯齿状→大锯齿状→临床前癌症→临床癌症（I-IV期））
2. 锯齿状腺瘤概率分配（总腺瘤病例的15%）
3. 锯齿状通路的独特进展参数
4. 与主要疾病进展引擎集成
5. 实现锯齿状腺瘤特异性进展率
6. 具有适当概率分布的通路选择逻辑

## Tasks / Subtasks

- [ ] 任务1：扩展疾病状态支持锯齿状通路 (AC: 1)

  - [ ] 扩展src/modules/disease/enums.py，添加锯齿状腺瘤状态
  - [ ] 实现SerratedAdenomaState枚举（小锯齿状、大锯齿状）
  - [ ] 创建锯齿状通路状态转换规则
  - [ ] 添加锯齿状腺瘤特异性属性
  - [ ] 实现锯齿状通路状态验证逻辑
  - [ ] 创建锯齿状通路状态转换图
- [ ] 任务2：实现通路选择和分配机制 (AC: 2, 6)

  - [ ] 创建src/modules/disease/pathway_selector.py文件
  - [ ] 实现PathwaySelector类，管理通路选择
  - [ ] 添加15%锯齿状腺瘤通路分配逻辑
  - [ ] 实现基于风险因素的通路选择偏好
  - [ ] 创建通路选择的随机抽样功能
  - [ ] 添加通路分配统计跟踪
- [ ] 任务3：实现锯齿状腺瘤进展参数 (AC: 3, 5)

  - [ ] 创建src/modules/disease/serrated_progression.py文件
  - [ ] 实现SerratedProgressionModel类
  - [ ] 添加锯齿状腺瘤特异性进展时间分布
  - [ ] 实现小锯齿状到大锯齿状转换建模
  - [ ] 创建大锯齿状到临床前癌症转换
  - [ ] 添加锯齿状通路进展率配置
- [ ] 任务4：集成锯齿状通路到主要疾病引擎 (AC: 4)

  - [ ] 扩展src/modules/disease/disease_model.py
  - [ ] 修改DiseaseProgressionEngine支持双通路
  - [ ] 实现通路特异性进展逻辑调度
  - [ ] 添加通路间转换验证和约束
  - [ ] 创建统一的疾病进展接口
  - [ ] 实现通路特异性统计收集
- [ ] 任务5：实现锯齿状腺瘤特异性特征 (AC: 1, 3)

  - [ ] 添加锯齿状腺瘤解剖位置偏好（更多近端结肠）
  - [ ] 实现锯齿状腺瘤筛查检测特征
  - [ ] 创建锯齿状腺瘤大小和形态特征
  - [ ] 添加锯齿状腺瘤恶变风险评估
  - [ ] 实现锯齿状腺瘤治疗反应特征
  - [ ] 创建锯齿状腺瘤预后因子
- [ ] 任务6：创建锯齿状通路测试和验证 (AC: 1-6)

  - [ ] 创建tests/unit/test_serrated_progression.py
  - [ ] 创建tests/unit/test_pathway_selector.py
  - [ ] 实现锯齿状通路进展测试
  - [ ] 添加通路选择概率验证测试
  - [ ] 创建双通路集成测试
  - [ ] 实现锯齿状通路统计验证

## Dev Notes

### 锯齿状腺瘤疾病状态扩展

```python
class DiseaseState(Enum):
    # 现有状态...
    SMALL_SERRATED_ADENOMA = "small_serrated_adenoma"
    LARGE_SERRATED_ADENOMA = "large_serrated_adenoma"
    # 其他状态保持不变...

class PathwayType(Enum):
    ADENOMA_CARCINOMA = "adenoma_carcinoma"      # 85%
    SERRATED_ADENOMA = "serrated_adenoma"        # 15%

# 锯齿状腺瘤特异性属性
@dataclass
class SerratedAdenomaFeatures:
    size_mm: float                    # 腺瘤大小（毫米）
    location_preference: float        # 近端结肠偏好（0-1）
    detection_difficulty: float       # 检测难度系数
    malignant_potential: float        # 恶变潜能评分
```

### 通路选择逻辑

```python
def select_pathway(individual: Individual, risk_factors: RiskFactorProfile) -> PathwayType:
    """选择疾病进展通路"""
    base_serrated_probability = 0.15  # 基础15%概率
  
    # 风险因素调整
    serrated_modifiers = {
        RiskFactorType.SMOKING: 1.3,      # 吸烟增加锯齿状风险
        RiskFactorType.ALCOHOL: 1.2,      # 酒精增加锯齿状风险
        RiskFactorType.FAMILY_HISTORY: 0.8 # 家族史降低锯齿状风险
    }
  
    adjusted_probability = base_serrated_probability
    for risk_factor in risk_factors.factors:
        if risk_factor.factor_type in serrated_modifiers and risk_factor.value:
            adjusted_probability *= serrated_modifiers[risk_factor.factor_type]
  
    # 性别调整（女性略高锯齿状风险）
    if individual.gender == "female":
        adjusted_probability *= 1.1
  
    return (PathwayType.SERRATED_ADENOMA 
            if random.random() < adjusted_probability 
            else PathwayType.ADENOMA_CARCINOMA)
```

### 锯齿状腺瘤进展参数

```python
SERRATED_PROGRESSION_TIMES = {
    "normal_to_small_serrated": {
        "mean": 3.0,      # 平均3年（比传统腺瘤快）
        "std": 1.5,       # 标准差1.5年
        "min": 0.5,       # 最小6个月
        "max": 8.0        # 最大8年
    },
    "small_to_large_serrated": {
        "mean": 4.0,      # 平均4年
        "std": 2.0,       # 标准差2年
        "min": 1.0,       # 最小1年
        "max": 10.0       # 最大10年
    },
    "large_serrated_to_preclinical": {
        "mean": 6.0,      # 平均6年（比传统腺瘤慢）
        "std": 2.5,       # 标准差2.5年
        "min": 2.0,       # 最小2年
        "max": 15.0       # 最大15年
    }
}
```

### 锯齿状腺瘤解剖位置偏好

```python
SERRATED_LOCATION_PROBABILITIES = {
    AnatomicalLocation.PROXIMAL_COLON: 0.65,  # 65%近端结肠（vs传统40%）
    AnatomicalLocation.DISTAL_COLON: 0.25,   # 25%远端结肠（vs传统35%）
    AnatomicalLocation.RECTUM: 0.10          # 10%直肠（vs传统25%）
}

# 锯齿状腺瘤筛查检测特征
SERRATED_SCREENING_CHARACTERISTICS = {
    "colonoscopy_sensitivity": 0.75,    # 结肠镜敏感性（vs传统0.95）
    "fit_sensitivity": 0.45,           # FIT敏感性（vs传统0.65）
    "sigmoidoscopy_sensitivity": 0.30,  # 乙状结肠镜敏感性（vs传统0.85）
    "detection_skill_dependency": 1.5   # 检测技能依赖性系数
}
```

### 双通路疾病进展引擎集成

```python
class DiseaseProgressionEngine:
    def __init__(self):
        self.adenoma_model = AdenomaProgressionModel()
        self.serrated_model = SerratedProgressionModel()
        self.pathway_selector = PathwaySelector()
  
    def progress_individual(self, individual: Individual, time_step: float):
        """根据通路类型进展个体疾病状态"""
        if individual.pathway_type == PathwayType.ADENOMA_CARCINOMA:
            return self.adenoma_model.progress(individual, time_step)
        elif individual.pathway_type == PathwayType.SERRATED_ADENOMA:
            return self.serrated_model.progress(individual, time_step)
        else:
            raise ValueError(f"Unknown pathway type: {individual.pathway_type}")
```

### 锯齿状腺瘤特异性风险因素

- **吸烟**: 增加锯齿状腺瘤风险1.3倍
- **酒精消费**: 增加锯齿状腺瘤风险1.2倍
- **女性性别**: 略高锯齿状腺瘤风险（1.1倍）
- **家族史**: 对锯齿状腺瘤保护作用（0.8倍）

### 锯齿状通路验证指标

- 通路分配比例: 锯齿状15% ± 2%
- 解剖位置分布: 近端结肠65% ± 5%
- 进展时间分布: 符合指定正态分布
- 筛查敏感性: 低于传统腺瘤通路

### Testing

#### 测试文件位置

- `tests/unit/test_serrated_progression.py`
- `tests/unit/test_pathway_selector.py`
- `tests/integration/test_dual_pathway.py`

#### 测试标准

- 通路选择概率准确性测试
- 锯齿状腺瘤进展时间分布验证
- 解剖位置分配概率测试
- 双通路集成功能测试
- 锯齿状腺瘤特异性特征测试

#### 测试框架和模式

- 大样本Monte Carlo模拟验证概率
- 统计检验验证分布拟合
- 参数化测试验证不同风险因素组合
- 集成测试验证双通路协调工作

#### 特定测试要求

- 通路分配准确性: 偏差 < 1%
- 进展时间分布拟合: p值 > 0.05
- 解剖位置分布准确性: 偏差 < 3%
- 双通路一致性: 无状态冲突

## Change Log

| Date       | Version | Description  | Author       |
| ---------- | ------- | ------------ | ------------ |
| 2025-07-31 | 1.0     | 初始故事创建 | Scrum Master |

## Dev Agent Record

*此部分将由开发代理在实施过程中填写*

### Agent Model Used

*待填写*

### Debug Log References

*待填写*

### Completion Notes List

*待填写*

### File List

*待填写*

## QA Results

*此部分将由QA代理在完成故事实施的QA审查后填写*
