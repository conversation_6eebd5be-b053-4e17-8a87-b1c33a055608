"""
锯齿状腺瘤进展模型

实现锯齿状腺瘤通路的疾病进展参数和时间分布建模。
"""

import random
import numpy as np
from typing import Dict, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum

from src.core.enums import DiseaseState, PathwayType


@dataclass
class ProgressionTimeDistribution:
    """进展时间分布参数"""
    mean: float          # 平均时间（年）
    std: float           # 标准差（年）
    min_time: float      # 最小时间（年）
    max_time: float      # 最大时间（年）
    distribution_type: str = "normal"  # 分布类型
    
    def __post_init__(self):
        """验证参数"""
        if self.mean <= 0:
            raise ValueError("平均时间必须大于0")
        if self.std <= 0:
            raise ValueError("标准差必须大于0")
        if self.min_time < 0:
            raise ValueError("最小时间不能为负数")
        if self.max_time <= self.min_time:
            raise ValueError("最大时间必须大于最小时间")
    
    def sample_time(self, random_seed: Optional[int] = None) -> float:
        """从分布中采样时间"""
        if random_seed is not None:
            np.random.seed(random_seed)
        
        if self.distribution_type == "normal":
            # 正态分布采样，截断到指定范围
            time = np.random.normal(self.mean, self.std)
            return max(self.min_time, min(self.max_time, time))
        elif self.distribution_type == "lognormal":
            # 对数正态分布
            time = np.random.lognormal(np.log(self.mean), self.std)
            return max(self.min_time, min(self.max_time, time))
        else:
            raise ValueError(f"不支持的分布类型: {self.distribution_type}")


class SerratedProgressionModel:
    """锯齿状腺瘤进展模型"""
    
    def __init__(self, random_seed: Optional[int] = None):
        """
        初始化锯齿状腺瘤进展模型
        
        Args:
            random_seed: 随机种子
        """
        self.random_seed = random_seed
        if random_seed is not None:
            random.seed(random_seed)
            np.random.seed(random_seed)
        
        # 锯齿状腺瘤进展时间分布参数
        self.progression_times = {
            "normal_to_small_serrated": ProgressionTimeDistribution(
                mean=3.0,      # 平均3年（比传统腺瘤快）
                std=1.5,       # 标准差1.5年
                min_time=0.5,  # 最小6个月
                max_time=8.0   # 最大8年
            ),
            "small_serrated_to_large_serrated": ProgressionTimeDistribution(
                mean=4.0,      # 平均4年
                std=2.0,       # 标准差2年
                min_time=1.0,  # 最小1年
                max_time=10.0  # 最大10年
            ),
            "large_serrated_to_preclinical_cancer": ProgressionTimeDistribution(
                mean=6.0,      # 平均6年（比传统腺瘤慢）
                std=2.5,       # 标准差2.5年
                min_time=2.0,  # 最小2年
                max_time=15.0  # 最大15年
            )
        }
        
        # 进展概率（每年）
        self.progression_probabilities = {
            "normal_to_small_serrated": 0.02,              # 2%年进展率
            "small_serrated_to_large_serrated": 0.15,      # 15%年进展率
            "large_serrated_to_preclinical_cancer": 0.08,  # 8%年进展率
        }
        
        # 回退概率（治疗或自然回退）
        self.regression_probabilities = {
            "small_serrated_to_normal": 0.05,      # 5%年回退率
            "large_serrated_to_normal": 0.03,      # 3%年回退率
        }
    
    def get_progression_time(self, from_state: str, to_state: str) -> float:
        """
        获取状态转换的进展时间
        
        Args:
            from_state: 起始状态
            to_state: 目标状态
            
        Returns:
            进展时间（年）
        """
        transition_key = f"{from_state}_to_{to_state}"
        
        if transition_key in self.progression_times:
            return self.progression_times[transition_key].sample_time(self.random_seed)
        else:
            raise ValueError(f"未定义的状态转换: {from_state} -> {to_state}")
    
    def get_progression_probability(self, from_state: str, to_state: str, time_step: float = 1.0) -> float:
        """
        获取在给定时间步长内的进展概率
        
        Args:
            from_state: 起始状态
            to_state: 目标状态
            time_step: 时间步长（年）
            
        Returns:
            进展概率
        """
        transition_key = f"{from_state}_to_{to_state}"
        
        if transition_key in self.progression_probabilities:
            annual_prob = self.progression_probabilities[transition_key]
            # 转换为时间步长概率
            return 1 - (1 - annual_prob) ** time_step
        elif transition_key in self.regression_probabilities:
            annual_prob = self.regression_probabilities[transition_key]
            return 1 - (1 - annual_prob) ** time_step
        else:
            return 0.0
    
    def should_progress(self, from_state: str, to_state: str, time_step: float = 1.0) -> bool:
        """
        判断是否应该进展到下一状态
        
        Args:
            from_state: 起始状态
            to_state: 目标状态
            time_step: 时间步长（年）
            
        Returns:
            是否进展
        """
        probability = self.get_progression_probability(from_state, to_state, time_step)
        return random.random() < probability
    
    def get_next_state(self, current_state: DiseaseState, time_step: float = 1.0) -> DiseaseState:
        """
        根据当前状态和时间步长确定下一状态
        
        Args:
            current_state: 当前疾病状态
            time_step: 时间步长（年）
            
        Returns:
            下一个疾病状态
        """
        state_str = current_state.value
        
        # 定义锯齿状通路的状态转换逻辑
        if state_str == "normal":
            if self.should_progress("normal", "small_serrated", time_step):
                return DiseaseState.SMALL_SERRATED
        elif state_str == "small_serrated":
            # 检查进展到大锯齿状腺瘤
            if self.should_progress("small_serrated", "large_serrated", time_step):
                return DiseaseState.LARGE_SERRATED
            # 检查回退到正常
            elif self.should_progress("small_serrated", "normal", time_step):
                return DiseaseState.NORMAL
        elif state_str == "large_serrated":
            # 检查进展到临床前癌症
            if self.should_progress("large_serrated", "preclinical_cancer", time_step):
                return DiseaseState.PRECLINICAL_CANCER
            # 检查回退到正常
            elif self.should_progress("large_serrated", "normal", time_step):
                return DiseaseState.NORMAL
        
        # 如果没有状态变化，返回当前状态
        return current_state
    
    def simulate_progression_timeline(self, start_state: DiseaseState, 
                                    max_time: float = 50.0) -> Dict[str, Any]:
        """
        模拟完整的进展时间线
        
        Args:
            start_state: 起始状态
            max_time: 最大模拟时间（年）
            
        Returns:
            进展时间线字典
        """
        timeline = []
        current_state = start_state
        current_time = 0.0
        
        while current_time < max_time:
            # 记录当前状态
            timeline.append({
                "time": current_time,
                "state": current_state.value,
                "pathway": "serrated_adenoma"
            })
            
            # 如果到达终态，停止模拟
            if current_state in [DiseaseState.DEATH_CANCER, DiseaseState.DEATH_OTHER]:
                break
            
            # 计算下一次状态变化的时间
            next_state = self.get_next_state(current_state, time_step=0.1)  # 0.1年步长
            
            if next_state != current_state:
                # 状态发生变化，计算精确的转换时间
                transition_time = self.get_progression_time(current_state.value, next_state.value)
                new_time = current_time + transition_time

                # 确保不超过最大时间
                if new_time > max_time:
                    break

                current_time = new_time
                current_state = next_state
            else:
                # 状态未变化，增加时间步长
                current_time += 0.1
        
        return {
            "timeline": timeline,
            "final_state": current_state.value,
            "total_time": current_time
        }
    
    def get_progression_statistics(self) -> Dict[str, Any]:
        """获取进展参数统计信息"""
        stats = {
            "progression_times": {},
            "progression_probabilities": self.progression_probabilities.copy(),
            "regression_probabilities": self.regression_probabilities.copy()
        }
        
        # 计算进展时间统计
        for transition, distribution in self.progression_times.items():
            stats["progression_times"][transition] = {
                "mean": distribution.mean,
                "std": distribution.std,
                "min": distribution.min_time,
                "max": distribution.max_time,
                "distribution_type": distribution.distribution_type
            }
        
        return stats
    
    def update_progression_parameters(self, parameter_updates: Dict[str, Any]) -> None:
        """
        更新进展参数
        
        Args:
            parameter_updates: 参数更新字典
        """
        if "progression_times" in parameter_updates:
            for transition, params in parameter_updates["progression_times"].items():
                if transition in self.progression_times:
                    current_dist = self.progression_times[transition]
                    # 更新分布参数
                    if "mean" in params:
                        current_dist.mean = params["mean"]
                    if "std" in params:
                        current_dist.std = params["std"]
                    if "min_time" in params:
                        current_dist.min_time = params["min_time"]
                    if "max_time" in params:
                        current_dist.max_time = params["max_time"]
        
        if "progression_probabilities" in parameter_updates:
            self.progression_probabilities.update(parameter_updates["progression_probabilities"])
        
        if "regression_probabilities" in parameter_updates:
            self.regression_probabilities.update(parameter_updates["regression_probabilities"])
    
    def validate_parameters(self) -> bool:
        """验证模型参数的合理性"""
        try:
            # 验证进展时间分布
            for transition, distribution in self.progression_times.items():
                distribution.__post_init__()  # 触发验证
            
            # 验证概率范围
            for prob_dict in [self.progression_probabilities, self.regression_probabilities]:
                for transition, prob in prob_dict.items():
                    if not 0 <= prob <= 1:
                        raise ValueError(f"概率 {transition} 超出有效范围: {prob}")
            
            return True
        except Exception:
            return False
