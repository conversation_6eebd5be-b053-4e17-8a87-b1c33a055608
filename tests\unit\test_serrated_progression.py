"""
测试锯齿状腺瘤进展模型

测试SerratedProgressionModel类的功能，包括进展时间分布、概率计算和状态转换。
"""

import pytest
import numpy as np
from unittest.mock import patch

from src.core.enums import DiseaseState
from src.modules.disease.serrated_progression import (
    ProgressionTimeDistribution,
    SerratedProgressionModel
)


class TestProgressionTimeDistribution:
    """测试进展时间分布"""
    
    def test_valid_distribution(self):
        """测试有效的分布创建"""
        dist = ProgressionTimeDistribution(
            mean=3.0,
            std=1.5,
            min_time=0.5,
            max_time=8.0
        )
        
        assert dist.mean == 3.0
        assert dist.std == 1.5
        assert dist.min_time == 0.5
        assert dist.max_time == 8.0
        assert dist.distribution_type == "normal"
    
    def test_invalid_parameters(self):
        """测试无效参数的验证"""
        # 负平均时间
        with pytest.raises(ValueError, match="平均时间必须大于0"):
            ProgressionTimeDistribution(mean=-1.0, std=1.0, min_time=0.0, max_time=5.0)
        
        # 负标准差
        with pytest.raises(ValueError, match="标准差必须大于0"):
            ProgressionTimeDistribution(mean=3.0, std=-1.0, min_time=0.0, max_time=5.0)
        
        # 负最小时间
        with pytest.raises(ValueError, match="最小时间不能为负数"):
            ProgressionTimeDistribution(mean=3.0, std=1.0, min_time=-1.0, max_time=5.0)
        
        # 最大时间小于等于最小时间
        with pytest.raises(ValueError, match="最大时间必须大于最小时间"):
            ProgressionTimeDistribution(mean=3.0, std=1.0, min_time=5.0, max_time=3.0)
    
    def test_time_sampling(self):
        """测试时间采样"""
        dist = ProgressionTimeDistribution(
            mean=3.0,
            std=1.0,
            min_time=1.0,
            max_time=6.0
        )
        
        # 测试多次采样
        samples = [dist.sample_time(random_seed=42) for _ in range(100)]
        
        # 检查采样结果在有效范围内
        assert all(1.0 <= sample <= 6.0 for sample in samples)
        
        # 检查采样结果的统计特性（大致符合预期）
        mean_sample = np.mean(samples)
        assert 2.0 <= mean_sample <= 4.0  # 允许一定偏差
    
    def test_lognormal_distribution(self):
        """测试对数正态分布"""
        dist = ProgressionTimeDistribution(
            mean=2.0,
            std=0.5,
            min_time=0.5,
            max_time=10.0,
            distribution_type="lognormal"
        )
        
        samples = [dist.sample_time(random_seed=42) for _ in range(50)]
        assert all(0.5 <= sample <= 10.0 for sample in samples)
    
    def test_unsupported_distribution(self):
        """测试不支持的分布类型"""
        dist = ProgressionTimeDistribution(
            mean=3.0,
            std=1.0,
            min_time=1.0,
            max_time=6.0,
            distribution_type="unsupported"
        )
        
        with pytest.raises(ValueError, match="不支持的分布类型"):
            dist.sample_time()


class TestSerratedProgressionModel:
    """测试锯齿状腺瘤进展模型"""
    
    def test_initialization(self):
        """测试模型初始化"""
        model = SerratedProgressionModel(random_seed=42)
        
        assert model.random_seed == 42
        assert "normal_to_small_serrated" in model.progression_times
        assert "small_serrated_to_large_serrated" in model.progression_times
        assert "large_serrated_to_preclinical_cancer" in model.progression_times

        assert "normal_to_small_serrated" in model.progression_probabilities
        assert "small_serrated_to_large_serrated" in model.progression_probabilities
        assert "large_serrated_to_preclinical_cancer" in model.progression_probabilities
    
    def test_progression_time_retrieval(self):
        """测试进展时间获取"""
        model = SerratedProgressionModel(random_seed=42)
        
        # 测试有效的状态转换
        time = model.get_progression_time("normal", "small_serrated")
        assert isinstance(time, float)
        assert time > 0
        
        time = model.get_progression_time("small_serrated", "large_serrated")
        assert isinstance(time, float)
        assert time > 0
        
        # 测试无效的状态转换
        with pytest.raises(ValueError, match="未定义的状态转换"):
            model.get_progression_time("invalid_state", "another_invalid_state")
    
    def test_progression_probability_calculation(self):
        """测试进展概率计算"""
        model = SerratedProgressionModel()
        
        # 测试年进展概率
        prob = model.get_progression_probability("normal", "small_serrated", time_step=1.0)
        assert 0 <= prob <= 1
        expected_prob = model.progression_probabilities["normal_to_small_serrated"]
        assert abs(prob - expected_prob) < 1e-10
        
        # 测试半年进展概率
        prob_half_year = model.get_progression_probability("normal", "small_serrated", time_step=0.5)
        assert 0 <= prob_half_year <= 1
        assert prob_half_year < prob  # 半年概率应该小于一年概率
        
        # 测试未定义的转换
        prob_undefined = model.get_progression_probability("invalid", "state", time_step=1.0)
        assert prob_undefined == 0.0
    
    def test_progression_decision(self):
        """测试进展决策"""
        model = SerratedProgressionModel(random_seed=42)
        
        # 测试多次决策的一致性
        decisions = []
        for _ in range(100):
            decision = model.should_progress("normal", "small_serrated", time_step=1.0)
            decisions.append(decision)
        
        # 应该有一些True和一些False（基于概率）
        true_count = sum(decisions)
        assert 0 < true_count < 100  # 不应该全是True或全是False
    
    def test_next_state_determination(self):
        """测试下一状态确定"""
        model = SerratedProgressionModel(random_seed=42)
        
        # 测试正常状态
        next_state = model.get_next_state(DiseaseState.NORMAL, time_step=1.0)
        assert next_state in [DiseaseState.NORMAL, DiseaseState.SMALL_SERRATED]
        
        # 测试小锯齿状腺瘤状态
        next_state = model.get_next_state(DiseaseState.SMALL_SERRATED, time_step=1.0)
        assert next_state in [
            DiseaseState.SMALL_SERRATED,
            DiseaseState.LARGE_SERRATED,
            DiseaseState.NORMAL
        ]
        
        # 测试大锯齿状腺瘤状态
        next_state = model.get_next_state(DiseaseState.LARGE_SERRATED, time_step=1.0)
        assert next_state in [
            DiseaseState.LARGE_SERRATED,
            DiseaseState.PRECLINICAL_CANCER,
            DiseaseState.NORMAL
        ]
    
    def test_progression_timeline_simulation(self):
        """测试进展时间线模拟"""
        model = SerratedProgressionModel(random_seed=42)
        
        timeline = model.simulate_progression_timeline(
            start_state=DiseaseState.NORMAL,
            max_time=20.0
        )
        
        assert "timeline" in timeline
        assert "final_state" in timeline
        assert "total_time" in timeline
        
        assert isinstance(timeline["timeline"], list)
        assert len(timeline["timeline"]) > 0
        
        # 检查时间线的第一个条目
        first_entry = timeline["timeline"][0]
        assert first_entry["time"] == 0.0
        assert first_entry["state"] == "normal"
        assert first_entry["pathway"] == "serrated_adenoma"
        
        # 检查总时间不超过最大时间（允许小的数值误差）
        assert timeline["total_time"] <= 20.1
    
    def test_progression_statistics(self):
        """测试进展统计信息"""
        model = SerratedProgressionModel()
        
        stats = model.get_progression_statistics()
        
        assert "progression_times" in stats
        assert "progression_probabilities" in stats
        assert "regression_probabilities" in stats
        
        # 检查进展时间统计
        assert "normal_to_small_serrated" in stats["progression_times"]
        time_stats = stats["progression_times"]["normal_to_small_serrated"]
        assert "mean" in time_stats
        assert "std" in time_stats
        assert "min" in time_stats
        assert "max" in time_stats
        
        # 检查概率统计
        assert "normal_to_small_serrated" in stats["progression_probabilities"]
        assert "small_serrated_to_normal" in stats["regression_probabilities"]
    
    def test_parameter_updates(self):
        """测试参数更新"""
        model = SerratedProgressionModel()
        
        # 更新进展时间参数
        updates = {
            "progression_times": {
                "normal_to_small_serrated": {
                    "mean": 4.0,
                    "std": 2.0
                }
            },
            "progression_probabilities": {
                "normal_to_small_serrated": 0.03
            }
        }
        
        model.update_progression_parameters(updates)
        
        # 验证更新
        assert model.progression_times["normal_to_small_serrated"].mean == 4.0
        assert model.progression_times["normal_to_small_serrated"].std == 2.0
        assert model.progression_probabilities["normal_to_small_serrated"] == 0.03
    
    def test_parameter_validation(self):
        """测试参数验证"""
        model = SerratedProgressionModel()
        
        # 有效参数应该通过验证
        assert model.validate_parameters() is True
        
        # 设置无效概率
        model.progression_probabilities["normal_to_small_serrated"] = 1.5  # 超出范围
        assert model.validate_parameters() is False
        
        # 恢复有效概率
        model.progression_probabilities["normal_to_small_serrated"] = 0.02
        assert model.validate_parameters() is True
    
    def test_progression_time_consistency(self):
        """测试进展时间的一致性"""
        model = SerratedProgressionModel(random_seed=42)
        
        # 使用相同种子应该产生相同结果
        time1 = model.get_progression_time("normal", "small_serrated")
        
        model2 = SerratedProgressionModel(random_seed=42)
        time2 = model2.get_progression_time("normal", "small_serrated")
        
        assert abs(time1 - time2) < 1e-10  # 应该完全相同
    
    def test_progression_probability_bounds(self):
        """测试进展概率边界"""
        model = SerratedProgressionModel()
        
        # 测试所有定义的转换概率
        for transition in model.progression_probabilities:
            prob = model.progression_probabilities[transition]
            assert 0 <= prob <= 1, f"概率 {transition} 超出范围: {prob}"
        
        for transition in model.regression_probabilities:
            prob = model.regression_probabilities[transition]
            assert 0 <= prob <= 1, f"概率 {transition} 超出范围: {prob}"
